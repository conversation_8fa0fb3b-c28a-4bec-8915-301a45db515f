# การตั้งค่า Google Sheets API

## ขั้นตอนการตั้งค่า

### 1. สร้าง Google Cloud Project
1. ไปที่ [Google Cloud Console](https://console.cloud.google.com/)
2. สร้างโปรเจกต์ใหม่หรือเลือกโปรเจกต์ที่มีอยู่
3. จดชื่อ Project ID ไว้

### 2. เปิดใช้งาน Google Sheets API
1. ใน Google Cloud Console ไปที่ "APIs & Services" > "Library"
2. ค้นหา "Google Sheets API"
3. คลิก "Enable"

### 3. สร้าง Service Account
1. ไปที่ "APIs & Services" > "Credentials"
2. คลิก "Create Credentials" > "Service Account"
3. ใส่ชื่อ Service Account (เช่น "leave-request-service")
4. คลิก "Create and Continue"
5. เลือก Role เป็น "Editor" หรือ "Owner"
6. คลิก "Done"

### 4. สร้าง JSON Key
1. ในหน้า Credentials คลิกที่ Service Account ที่สร้างไว้
2. ไปที่แท็บ "Keys"
3. คลิก "Add Key" > "Create new key"
4. เลือก "JSON" และคลิก "Create"
5. ไฟล์ JSON จะถูกดาวน์โหลดมา

### 5. สร้าง Google Sheets
1. ไปที่ [Google Sheets](https://sheets.google.com/)
2. สร้าง Spreadsheet ใหม่
3. ตั้งชื่อเป็น "Leave Requests" หรือชื่อที่ต้องการ
4. คัดลอก Spreadsheet ID จาก URL
   ```
   https://docs.google.com/spreadsheets/d/[SPREADSHEET_ID]/edit
   ```

### 6. แชร์ Google Sheets
1. ในไฟล์ Google Sheets คลิก "Share"
2. ใส่อีเมลของ Service Account (จากไฟล์ JSON ในฟิลด์ `client_email`)
3. ให้สิทธิ์ "Editor"
4. คลิก "Send"

### 7. ตั้งค่า Environment Variables
1. เปิดไฟล์ `.env.local`
2. นำข้อมูลจากไฟล์ JSON มาใส่:

```env
GOOGLE_SHEETS_ID=your-spreadsheet-id-from-url
GOOGLE_PROJECT_ID=project_id-from-json
GOOGLE_PRIVATE_KEY_ID=private_key_id-from-json
GOOGLE_PRIVATE_KEY="private_key-from-json"
GOOGLE_CLIENT_EMAIL=client_email-from-json
GOOGLE_CLIENT_ID=client_id-from-json
GOOGLE_CLIENT_X509_CERT_URL=client_x509_cert_url-from-json
```

### 8. ทดสอบระบบ
1. รีสตาร์ท Next.js server
   ```bash
   npm run dev
   ```
2. ไปที่หน้า "ลงเวลาลา" และทดสอบส่งฟอร์ม
3. ตรวจสอบว่าข้อมูลปรากฏใน Google Sheets

## ตัวอย่างโครงสร้างใน Google Sheets

ระบบจะสร้างหัวตารางดังนี้:

| วันที่ส่งคำขอ | ชื่อ-นามสกุล | รหัสพนักงาน | แผนก | ประเภทการลา | วันที่เริ่มลา | วันที่สิ้นสุดการลา | เหตุผล | เบอร์โทร | อีเมล | สถานะ |
|-------------|------------|------------|------|------------|------------|------------------|--------|---------|-------|-------|

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย:
1. **403 Forbidden**: ตรวจสอบว่าได้แชร์ Google Sheets ให้กับ Service Account แล้ว
2. **404 Not Found**: ตรวจสอบ GOOGLE_SHEETS_ID ว่าถูกต้อง
3. **Invalid credentials**: ตรวจสอบข้อมูลใน .env.local ว่าถูกต้อง

### การตรวจสอบ:
1. ดูใน Console ของเบราว์เซอร์หากมี error
2. ตรวจสอบ Network tab ว่า API call สำเร็จหรือไม่
3. ดูใน Google Sheets ว่าข้อมูลถูกบันทึกหรือไม่

## ความปลอดภัย

⚠️ **คำเตือน**: 
- ไม่ควรเผยแพร่ไฟล์ `.env.local` หรือ JSON key file
- ใส่ `.env.local` ใน `.gitignore`
- ใช้สิทธิ์ขั้นต่ำที่จำเป็นสำหรับ Service Account
