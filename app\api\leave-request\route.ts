import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';

// Google Sheets configuration
const SPREADSHEET_ID = process.env.GOOGLE_SHEETS_ID || 'your-spreadsheet-id';
const SHEET_NAME = 'Leave Requests';

// Google Service Account credentials
const credentials = {
  type: 'service_account',
  project_id: process.env.GOOGLE_PROJECT_ID,
  private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,
  private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  client_email: process.env.GOOGLE_CLIENT_EMAIL,
  client_id: process.env.GOOGLE_CLIENT_ID,
  auth_uri: 'https://accounts.google.com/o/oauth2/auth',
  token_uri: 'https://oauth2.googleapis.com/token',
  auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
  client_x509_cert_url: process.env.GOOGLE_CLIENT_X509_CERT_URL,
};

async function getGoogleSheetsClient() {
  const auth = new google.auth.GoogleAuth({
    credentials,
    scopes: ['https://www.googleapis.com/auth/spreadsheets'],
  });

  const sheets = google.sheets({ version: 'v4', auth });
  return sheets;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      employeeName,
      employeeId,
      department,
      leaveType,
      startDate,
      endDate,
      reason,
      contactNumber,
      email,
      submittedAt
    } = body;

    // Validate required fields
    if (!employeeName || !employeeId || !department || !leaveType || !startDate || !endDate || !reason || !contactNumber || !email) {
      return NextResponse.json(
        { error: 'กรุณากรอกข้อมูลให้ครบถ้วน' },
        { status: 400 }
      );
    }

    // Check if environment variables are set
    if (!process.env.GOOGLE_CLIENT_EMAIL || !process.env.GOOGLE_PRIVATE_KEY) {
      console.error('Google Sheets credentials not configured');
      return NextResponse.json(
        { error: 'ระบบยังไม่ได้ตั้งค่าการเชื่อมต่อ Google Sheets' },
        { status: 500 }
      );
    }

    const sheets = await getGoogleSheetsClient();

    // Prepare data for Google Sheets
    const leaveTypeMap: { [key: string]: string } = {
      sick: 'ลาป่วย',
      personal: 'ลากิจ',
      annual: 'ลาพักร้อน',
      maternity: 'ลาคลอด',
      other: 'อื่นๆ'
    };

    const rowData = [
      new Date(submittedAt).toLocaleString('th-TH', { timeZone: 'Asia/Bangkok' }),
      employeeName,
      employeeId,
      department,
      leaveTypeMap[leaveType] || leaveType,
      new Date(startDate).toLocaleDateString('th-TH'),
      new Date(endDate).toLocaleDateString('th-TH'),
      reason,
      contactNumber,
      email,
      'รอการอนุมัติ' // Status
    ];

    // Check if sheet exists and has headers
    try {
      const sheetInfo = await sheets.spreadsheets.get({
        spreadsheetId: SPREADSHEET_ID,
      });

      const sheetExists = sheetInfo.data.sheets?.some(
        sheet => sheet.properties?.title === SHEET_NAME
      );

      if (!sheetExists) {
        // Create the sheet if it doesn't exist
        await sheets.spreadsheets.batchUpdate({
          spreadsheetId: SPREADSHEET_ID,
          requestBody: {
            requests: [
              {
                addSheet: {
                  properties: {
                    title: SHEET_NAME,
                  },
                },
              },
            ],
          },
        });

        // Add headers
        const headers = [
          'วันที่ส่งคำขอ',
          'ชื่อ-นามสกุล',
          'รหัสพนักงาน',
          'แผนก',
          'ประเภทการลา',
          'วันที่เริ่มลา',
          'วันที่สิ้นสุดการลา',
          'เหตุผล',
          'เบอร์โทร',
          'อีเมล',
          'สถานะ'
        ];

        await sheets.spreadsheets.values.update({
          spreadsheetId: SPREADSHEET_ID,
          range: `${SHEET_NAME}!A1:K1`,
          valueInputOption: 'RAW',
          requestBody: {
            values: [headers],
          },
        });
      }
    } catch (error) {
      console.error('Error checking/creating sheet:', error);
    }

    // Append the new row
    await sheets.spreadsheets.values.append({
      spreadsheetId: SPREADSHEET_ID,
      range: `${SHEET_NAME}!A:K`,
      valueInputOption: 'RAW',
      requestBody: {
        values: [rowData],
      },
    });

    return NextResponse.json(
      { 
        message: 'บันทึกข้อมูลสำเร็จ',
        data: {
          employeeName,
          leaveType: leaveTypeMap[leaveType] || leaveType,
          startDate,
          endDate
        }
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error saving to Google Sheets:', error);
    return NextResponse.json(
      { error: 'เกิดข้อผิดพลาดในการบันทึกข้อมูล' },
      { status: 500 }
    );
  }
}
