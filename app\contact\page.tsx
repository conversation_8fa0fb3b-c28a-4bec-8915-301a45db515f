export default function Contact() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold text-gray-800 mb-6">ติดต่อเรา</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-4">ข้อมูลติดต่อ</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <span>📧</span>
              <span><EMAIL></span>
            </div>
            <div className="flex items-center space-x-3">
              <span>📞</span>
              <span>02-123-4567</span>
            </div>
            <div className="flex items-center space-x-3">
              <span>📍</span>
              <span>กรุงเทพมหานคร, ประเทศไทย</span>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-4">ส่งข้อความ</h3>
          <form className="space-y-4">
            <input
              type="text"
              placeholder="ชื่อของคุณ"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <input
              type="email"
              placeholder="อีเมลของคุณ"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <textarea
              placeholder="ข้อความของคุณ"
              rows={4}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            ></textarea>
            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-3 rounded-md hover:bg-blue-700 transition-colors duration-200"
            >
              ส่งข้อความ
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
