import type { Metada<PERSON> } from "next";
import "./globals.css";
import MenuBar from "./components/MenuBar";


export const metadata: Metadata = {
  title: "New Day",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`antialiased`}
      >
        <MenuBar />
        <main className="min-h-screen">
          {children}
        </main>
      </body>
    </html>
  );
}
