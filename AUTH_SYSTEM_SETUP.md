# การสร้างระบบล็อกอินและสมัครสมาชิกด้วย MySQL

## 📋 สารบัญ
1. [การติดตั้ง Dependencies](#1-การติดตั้ง-dependencies)
2. [การตั้งค่า MySQL Database](#2-การตั้งค่า-mysql-database)
3. [การสร้าง Database Schema](#3-การสร้าง-database-schema)
4. [การตั้งค่า Environment Variables](#4-การตั้งค่า-environment-variables)
5. [การสร้าง Database Connection](#5-การสร้าง-database-connection)
6. [การสร้าง API Routes](#6-การสร้าง-api-routes)
7. [การสร้างหน้า UI](#7-การสร้างหน้า-ui)
8. [การจัดการ Session](#8-การจัดการ-session)
9. [การทดสอบระบบ](#9-การทดสอบระบบ)
10. [การแก้ไขปัญหา](#10-การแก้ไขปัญหา)

---

## 1. การติดตั้ง Dependencies

### 1.1 ติดตั้ง Package หลัก
```bash
npm install mysql2 bcryptjs jsonwebtoken
npm install @types/bcryptjs @types/jsonwebtoken --save-dev
```

### 1.2 ติดตั้ง Package เสริม (Optional)
```bash
# สำหรับ ORM (ถ้าต้องการ)
npm install prisma @prisma/client

# สำหรับ Validation
npm install zod

# สำหรับ Session Management
npm install next-auth
```

### 1.3 รายละเอียด Dependencies
- **mysql2**: MySQL client สำหรับ Node.js
- **bcryptjs**: สำหรับ hash password
- **jsonwebtoken**: สำหรับสร้างและตรวจสอบ JWT tokens
- **zod**: สำหรับ validation ข้อมูล
- **next-auth**: สำหรับจัดการ authentication (optional)

---

## 2. การตั้งค่า MySQL Database

### 2.1 ติดตั้ง MySQL Server

#### Windows:
1. ดาวน์โหลด MySQL Installer จาก [mysql.com](https://dev.mysql.com/downloads/installer/)
2. เลือก "MySQL Server" และ "MySQL Workbench"
3. ตั้งค่า root password
4. เริ่มต้น MySQL Service

#### macOS (ใช้ Homebrew):
```bash
brew install mysql
brew services start mysql
mysql_secure_installation
```

#### Linux (Ubuntu):
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

### 2.2 สร้าง Database และ User

```sql
-- เข้าสู่ MySQL console
mysql -u root -p

-- สร้าง database
CREATE DATABASE newday_auth;

-- สร้าง user สำหรับ application
CREATE USER 'newday_user'@'localhost' IDENTIFIED BY 'secure_password_123';

-- ให้สิทธิ์ user
GRANT ALL PRIVILEGES ON newday_auth.* TO 'newday_user'@'localhost';
FLUSH PRIVILEGES;

-- ตรวจสอบ
SHOW DATABASES;
USE newday_auth;
```

### 2.3 การใช้ MySQL Workbench (GUI)
1. เปิด MySQL Workbench
2. สร้าง Connection ใหม่
3. ใส่ข้อมูล: Host=localhost, Port=3306, Username=root
4. ทดสอบ Connection
5. สร้าง Schema ใหม่ชื่อ "newday_auth"

---

## 3. การสร้าง Database Schema

### 3.1 ตาราง Users
```sql
USE newday_auth;

CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('user', 'admin', 'hr') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_created_at (created_at)
);
```

### 3.2 ตาราง User Sessions
```sql
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_session_token (session_token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);
```

### 3.3 ตาราง Password Reset Tokens
```sql
CREATE TABLE password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);
```

### 3.4 ตาราง Email Verification Tokens
```sql
CREATE TABLE email_verification_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id)
);
```

### 3.5 ตาราง User Profiles (เสริม)
```sql
CREATE TABLE user_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNIQUE NOT NULL,
    avatar_url VARCHAR(500),
    bio TEXT,
    birth_date DATE,
    gender ENUM('male', 'female', 'other'),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

---

## 4. การตั้งค่า Environment Variables

### 4.1 สร้างไฟล์ .env.local
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=newday_auth
DB_USER=newday_user
DB_PASSWORD=secure_password_123

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
JWT_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=your-session-secret-key-here
SESSION_EXPIRES_IN=86400

# Email Configuration (สำหรับส่งอีเมลยืนยัน)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Application Configuration
NEXTAUTH_URL=http://localhost:3001
NEXTAUTH_SECRET=your-nextauth-secret-here

# Security
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# Development
NODE_ENV=development
```

### 4.2 การสร้าง Secret Keys
```bash
# สร้าง random secret สำหรับ JWT
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# หรือใช้ online generator
# https://generate-secret.vercel.app/64
```

---

## 5. การสร้าง Database Connection

### 5.1 สร้างไฟล์ lib/database.ts
```typescript
import mysql from 'mysql2/promise';

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'newday_auth',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
};

// สร้าง connection pool
const pool = mysql.createPool(dbConfig);

// ฟังก์ชันทดสอบการเชื่อมต่อ
export async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ Database connected successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// ฟังก์ชัน execute query
export async function executeQuery(query: string, params: any[] = []) {
  try {
    const [results] = await pool.execute(query, params);
    return results;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
}

// ฟังก์ชัน execute transaction
export async function executeTransaction(queries: Array<{query: string, params: any[]}>) {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    
    const results = [];
    for (const {query, params} of queries) {
      const [result] = await connection.execute(query, params);
      results.push(result);
    }
    
    await connection.commit();
    return results;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

export default pool;
```

### 5.2 สร้างไฟล์ lib/auth.ts
```typescript
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { executeQuery } from './database';

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret';
const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12');

// Interface สำหรับ User
export interface User {
  id: number;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role: 'user' | 'admin' | 'hr';
  is_active: boolean;
  email_verified: boolean;
  created_at: Date;
  last_login?: Date;
}

// ฟังก์ชัน hash password
export async function hashPassword(password: string): Promise<string> {
  return await bcrypt.hash(password, BCRYPT_ROUNDS);
}

// ฟังก์ชันตรวจสอบ password
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return await bcrypt.compare(password, hash);
}

// ฟังก์ชันสร้าง JWT token
export function generateToken(payload: any): string {
  return jwt.sign(payload, JWT_SECRET, { 
    expiresIn: process.env.JWT_EXPIRES_IN || '7d' 
  });
}

// ฟังก์ชันตรวจสอบ JWT token
export function verifyToken(token: string): any {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

// ฟังก์ชันค้นหา user ด้วย email
export async function findUserByEmail(email: string): Promise<User | null> {
  const query = 'SELECT * FROM users WHERE email = ? AND is_active = TRUE';
  const results = await executeQuery(query, [email]) as any[];
  return results.length > 0 ? results[0] : null;
}

// ฟังก์ชันค้นหา user ด้วย username
export async function findUserByUsername(username: string): Promise<User | null> {
  const query = 'SELECT * FROM users WHERE username = ? AND is_active = TRUE';
  const results = await executeQuery(query, [username]) as any[];
  return results.length > 0 ? results[0] : null;
}

// ฟังก์ชันค้นหา user ด้วย ID
export async function findUserById(id: number): Promise<User | null> {
  const query = 'SELECT * FROM users WHERE id = ? AND is_active = TRUE';
  const results = await executeQuery(query, [id]) as any[];
  return results.length > 0 ? results[0] : null;
}

// ฟังก์ชันสร้าง user ใหม่
export async function createUser(userData: {
  email: string;
  username: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
}): Promise<User> {
  const hashedPassword = await hashPassword(userData.password);
  
  const query = `
    INSERT INTO users (email, username, password_hash, first_name, last_name, phone)
    VALUES (?, ?, ?, ?, ?, ?)
  `;
  
  const result = await executeQuery(query, [
    userData.email,
    userData.username,
    hashedPassword,
    userData.first_name,
    userData.last_name,
    userData.phone || null
  ]) as any;
  
  const newUser = await findUserById(result.insertId);
  if (!newUser) {
    throw new Error('Failed to create user');
  }
  
  return newUser;
}

// ฟังก์ชันอัปเดต last_login
export async function updateLastLogin(userId: number): Promise<void> {
  const query = 'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?';
  await executeQuery(query, [userId]);
}
```

---

## 6. การสร้าง API Routes

### 6.1 สร้างไฟล์ app/api/auth/register/route.ts
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { createUser, findUserByEmail, findUserByUsername } from '@/lib/auth';
import { z } from 'zod';

// Schema สำหรับ validation
const registerSchema = z.object({
  email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง'),
  username: z.string().min(3, 'ชื่อผู้ใช้ต้องมีอย่างน้อย 3 ตัวอักษร').max(20, 'ชื่อผู้ใช้ต้องไม่เกิน 20 ตัวอักษร'),
  password: z.string().min(8, 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร'),
  confirmPassword: z.string(),
  firstName: z.string().min(1, 'กรุณากรอกชื่อ'),
  lastName: z.string().min(1, 'กรุณากรอกนามสกุล'),
  phone: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "รหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input
    const validatedData = registerSchema.parse(body);

    // ตรวจสอบว่าอีเมลซ้ำหรือไม่
    const existingUserByEmail = await findUserByEmail(validatedData.email);
    if (existingUserByEmail) {
      return NextResponse.json(
        { error: 'อีเมลนี้ถูกใช้งานแล้ว' },
        { status: 400 }
      );
    }

    // ตรวจสอบว่าชื่อผู้ใช้ซ้ำหรือไม่
    const existingUserByUsername = await findUserByUsername(validatedData.username);
    if (existingUserByUsername) {
      return NextResponse.json(
        { error: 'ชื่อผู้ใช้นี้ถูกใช้งานแล้ว' },
        { status: 400 }
      );
    }

    // สร้างผู้ใช้ใหม่
    const newUser = await createUser({
      email: validatedData.email,
      username: validatedData.username,
      password: validatedData.password,
      first_name: validatedData.firstName,
      last_name: validatedData.lastName,
      phone: validatedData.phone,
    });

    // ส่งข้อมูลกลับ (ไม่รวม password)
    const { password_hash, ...userWithoutPassword } = newUser as any;

    return NextResponse.json({
      message: 'สมัครสมาชิกสำเร็จ',
      user: userWithoutPassword
    }, { status: 201 });

  } catch (error) {
    console.error('Registration error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'ข้อมูลไม่ถูกต้อง', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'เกิดข้อผิดพลาดในการสมัครสมาชิก' },
      { status: 500 }
    );
  }
}
```

### 6.2 สร้างไฟล์ app/api/auth/login/route.ts
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { findUserByEmail, findUserByUsername, verifyPassword, generateToken, updateLastLogin } from '@/lib/auth';
import { z } from 'zod';

// Schema สำหรับ validation
const loginSchema = z.object({
  identifier: z.string().min(1, 'กรุณากรอกอีเมลหรือชื่อผู้ใช้'),
  password: z.string().min(1, 'กรุณากรอกรหัสผ่าน'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input
    const validatedData = loginSchema.parse(body);

    // ค้นหาผู้ใช้ (อีเมลหรือชื่อผู้ใช้)
    let user = await findUserByEmail(validatedData.identifier);
    if (!user) {
      user = await findUserByUsername(validatedData.identifier);
    }

    if (!user) {
      return NextResponse.json(
        { error: 'ไม่พบผู้ใช้งาน' },
        { status: 401 }
      );
    }

    // ตรวจสอบรหัสผ่าน
    const isPasswordValid = await verifyPassword(validatedData.password, (user as any).password_hash);
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'รหัสผ่านไม่ถูกต้อง' },
        { status: 401 }
      );
    }

    // ตรวจสอบสถานะบัญชี
    if (!user.is_active) {
      return NextResponse.json(
        { error: 'บัญชีถูกระงับการใช้งาน' },
        { status: 401 }
      );
    }

    // อัปเดต last_login
    await updateLastLogin(user.id);

    // สร้าง JWT token
    const token = generateToken({
      userId: user.id,
      email: user.email,
      username: user.username,
      role: user.role
    });

    // ส่งข้อมูลกลับ (ไม่รวม password)
    const { password_hash, ...userWithoutPassword } = user as any;

    // สร้าง response พร้อม set cookie
    const response = NextResponse.json({
      message: 'เข้าสู่ระบบสำเร็จ',
      user: userWithoutPassword,
      token
    });

    // Set HTTP-only cookie
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      path: '/'
    });

    return response;

  } catch (error) {
    console.error('Login error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'ข้อมูลไม่ถูกต้อง', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'เกิดข้อผิดพลาดในการเข้าสู่ระบบ' },
      { status: 500 }
    );
  }
}
```

### 6.3 สร้างไฟล์ app/api/auth/logout/route.ts
```typescript
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const response = NextResponse.json({
      message: 'ออกจากระบบสำเร็จ'
    });

    // ลบ auth cookie
    response.cookies.set('auth-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/'
    });

    return response;

  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: 'เกิดข้อผิดพลาดในการออกจากระบบ' },
      { status: 500 }
    );
  }
}
```

### 6.4 สร้างไฟล์ app/api/auth/me/route.ts
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken, findUserById } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // ดึง token จาก cookie หรือ header
    const token = request.cookies.get('auth-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { error: 'ไม่พบ token การยืนยันตัวตน' },
        { status: 401 }
      );
    }

    // ตรวจสอบ token
    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json(
        { error: 'Token ไม่ถูกต้องหรือหมดอายุ' },
        { status: 401 }
      );
    }

    // ค้นหาผู้ใช้
    const user = await findUserById(decoded.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'ไม่พบผู้ใช้งาน' },
        { status: 404 }
      );
    }

    // ส่งข้อมูลผู้ใช้กลับ (ไม่รวม password)
    const { password_hash, ...userWithoutPassword } = user as any;

    return NextResponse.json({
      user: userWithoutPassword
    });

  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json(
      { error: 'เกิดข้อผิดพลาดในการดึงข้อมูลผู้ใช้' },
      { status: 500 }
    );
  }
}
```

---

## 7. การสร้างหน้า UI

### 7.1 สร้างไฟล์ app/auth/register/page.tsx
```typescript
'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function RegisterPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: ''
  });

  const [errors, setErrors] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // ลบ error เมื่อผู้ใช้เริ่มพิมพ์
    if (errors[name]) {
      setErrors((prev: any) => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok) {
        alert('สมัครสมาชิกสำเร็จ! กรุณาเข้าสู่ระบบ');
        router.push('/auth/login');
      } else {
        if (data.details) {
          // Zod validation errors
          const fieldErrors: any = {};
          data.details.forEach((error: any) => {
            fieldErrors[error.path[0]] = error.message;
          });
          setErrors(fieldErrors);
        } else {
          setErrors({ general: data.error });
        }
      }
    } catch (error) {
      console.error('Registration error:', error);
      setErrors({ general: 'เกิดข้อผิดพลาดในการสมัครสมาชิก' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            สมัครสมาชิก
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            หรือ{' '}
            <Link href="/auth/login" className="font-medium text-blue-600 hover:text-blue-500">
              เข้าสู่ระบบ
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {errors.general && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {errors.general}
            </div>
          )}

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                  ชื่อ *
                </label>
                <input
                  id="firstName"
                  name="firstName"
                  type="text"
                  required
                  value={formData.firstName}
                  onChange={handleChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                {errors.firstName && <p className="text-red-500 text-xs mt-1">{errors.firstName}</p>}
              </div>

              <div>
                <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                  นามสกุล *
                </label>
                <input
                  id="lastName"
                  name="lastName"
                  type="text"
                  required
                  value={formData.lastName}
                  onChange={handleChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                {errors.lastName && <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>}
              </div>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                อีเมล *
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
            </div>

            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                ชื่อผู้ใช้ *
              </label>
              <input
                id="username"
                name="username"
                type="text"
                required
                value={formData.username}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.username && <p className="text-red-500 text-xs mt-1">{errors.username}</p>}
            </div>

            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                เบอร์โทรศัพท์
              </label>
              <input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                รหัสผ่าน *
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                ยืนยันรหัสผ่าน *
              </label>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                required
                value={formData.confirmPassword}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.confirmPassword && <p className="text-red-500 text-xs mt-1">{errors.confirmPassword}</p>}
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'กำลังสมัครสมาชิก...' : 'สมัครสมาชิก'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
```
```
