export default function LeaveRequestDemo() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-800 mb-6 text-center">
          ตัวอย่างข้อมูลการลงเวลาลา
        </h1>
        
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="bg-blue-600 text-white p-4">
            <h2 className="text-xl font-semibold">📊 ข้อมูลที่บันทึกใน Google Sheets</h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    วันที่ส่งคำขอ
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ชื่อ-นามสกุล
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    รหัสพนักงาน
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    แผนก
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ประเภทการลา
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    วันที่ลา
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    สถานะ
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    15/8/2025 14:30
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    สมชาย ใจดี
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    EMP001
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    IT
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                      ลาป่วย
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    16/8/2025 - 17/8/2025
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      รอการอนุมัติ
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    15/8/2025 10:15
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    สมหญิง รักงาน
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    EMP002
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    HR
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      ลากิจ
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    20/8/2025 - 20/8/2025
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      อนุมัติแล้ว
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    14/8/2025 16:45
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    สมศักดิ์ ขยันทำงาน
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    EMP003
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    การเงิน
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                      ลาพักร้อน
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    25/8/2025 - 29/8/2025
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      รอการอนุมัติ
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">3</div>
            <div className="text-gray-600">คำขอทั้งหมด</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <div className="text-3xl font-bold text-yellow-600 mb-2">2</div>
            <div className="text-gray-600">รอการอนุมัติ</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">1</div>
            <div className="text-gray-600">อนุมัติแล้ว</div>
          </div>
        </div>

        <div className="mt-8 bg-green-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800 mb-3">✅ ฟีเจอร์ที่ใช้งานได้:</h3>
          <ul className="text-green-700 space-y-2">
            <li>• ✅ ส่งข้อมูลไป Google Sheets โดยอัตโนมัติ</li>
            <li>• ✅ บันทึกวันที่และเวลาที่ส่งคำขอ</li>
            <li>• ✅ ตรวจสอบข้อมูลที่จำเป็นก่อนส่ง</li>
            <li>• ✅ แสดงสถานะการส่งข้อมูล</li>
            <li>• ✅ รองรับการลาหลายประเภท</li>
            <li>• ✅ ฟอร์มที่ใช้งานง่ายและสวยงาม</li>
          </ul>
        </div>

        <div className="mt-6 bg-blue-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800 mb-3">🔧 การตั้งค่าที่จำเป็น:</h3>
          <ul className="text-blue-700 space-y-2">
            <li>• สร้าง Google Cloud Project และเปิดใช้งาน Google Sheets API</li>
            <li>• สร้าง Service Account และดาวน์โหลด JSON key</li>
            <li>• สร้าง Google Sheets และแชร์ให้กับ Service Account</li>
            <li>• ตั้งค่า Environment Variables ในไฟล์ .env.local</li>
            <li>• ดูรายละเอียดใน <code className="bg-blue-200 px-2 py-1 rounded">GOOGLE_SHEETS_SETUP.md</code></li>
          </ul>
        </div>

        <div className="mt-6 text-center">
          <a
            href="/leave-request"
            className="inline-block bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors duration-200"
          >
            📝 ไปที่หน้าลงเวลาลา
          </a>
        </div>
      </div>
    </div>
  );
}
